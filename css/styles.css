/* Global Styles */
:root {
    --primary-color: #D90000;
    /* Deep Red */
    --secondary-color: #FF3333;
    /* Lighter Red */
    --dark-color: #000000;
    /* Black */
    --light-color: #FFFFFF;
    /* White */
    --gray-color: #121212;
    /* Very Dark Gray for backgrounds */
    --dark-gray: #1a1a1a;
    /* Slightly lighter dark gray */
    --text-gray: #FFFFFF;
    /* White for better readability */
    --card-bg: #1a1a1a;
    /* Card background */
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-gray);
    line-height: 1.6;
    background-color: var(--gray-color);
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: '<PERSON>', sans-serif;
    font-weight: 700;
    text-transform: uppercase;
    color: var(--light-color);
}

.btn-danger {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 0 0 15px rgba(217, 0, 0, 0.3);
}

.btn-danger:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    box-shadow: 0 0 20px rgba(217, 0, 0, 0.5);
}

.section-title {
    font-family: 'Oswald', sans-serif;
    font-weight: 700;
    font-size: 36px;
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
    color: var(--light-color);
    display: flex;
    justify-content: center;
    align-items: center;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    width: 60px;
    height: 3px;
    background-color: #dc3545;
    left: 50%;
    transform: translateX(-50%);
}

.text-center.section-title::after {
    margin-left: auto;
    margin-right: auto;
}

/* Navigation */
.navbar {
    background-color: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 1030; /* Ensure navbar is above other content */
    position: relative;
}

/* Navbar toggler improvements for mobile */
.navbar-toggler {
    border: none;
    padding: 4px 8px;
    z-index: 1031;
    position: relative;
}

.navbar-toggler:focus {
    box-shadow: none;
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.85%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    width: 1.5em;
    height: 1.5em;
}

/* Navbar collapse improvements */
.navbar-collapse {
    z-index: 1030;
    position: relative;
}

/* Mobile menu improvements */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background-color: rgba(0, 0, 0, 0.95);
        margin-top: 10px;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        position: absolute;
        top: 100%;
        left: 15px;
        right: 15px;
        z-index: 1030;
    }

    .navbar-nav .nav-link {
        padding: 12px 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        color: white !important;
    }

    .navbar-nav .nav-link:last-child {
        border-bottom: none;
    }

    .navbar-nav .nav-link:hover {
        background-color: rgba(220, 53, 69, 0.1);
        border-radius: 5px;
        color: var(--primary-color) !important;
    }

    /* Ensure navbar container has relative positioning */
    .navbar .container {
        position: relative;
    }

    /* Fix for hamburger button visibility */
    .navbar-toggler {
        background-color: transparent;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        padding: 6px 10px;
    }

    .navbar-toggler:hover {
        background-color: rgba(220, 53, 69, 0.1);
        border-color: var(--primary-color);
    }
}

.navbar-brand {
    font-family: 'Oswald', sans-serif;
    font-weight: 700;
    font-size: 1.8rem;
    color: var(--light-color);
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--light-color);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0.5rem 1rem;
    transition: color 0.3s;
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link.active {
    color: var(--primary-color);
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    min-height: 600px;
    background-color: #000;
    display: flex;
    align-items: center;
    color: var(--light-color);
    text-align: center;
    padding-top: 80px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7));
    z-index: 1;
}

.hero .container {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-family: 'Oswald', sans-serif;
    font-size: 4.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    letter-spacing: 3px;
    color: #fff;
    text-transform: uppercase;
}

.hero h2 {
    font-family: 'Oswald', sans-serif;
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
    color: #fff;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #fff;
}

.hero .btn-danger {
    padding: 12px 30px;
    font-size: 1.2rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    background-color: #D90000;
    border: none;
    box-shadow: 0 4px 15px rgba(217, 0, 0, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hero .btn-danger:hover {
    transform: translateY(-2px);
    background-color: #FF0000;
    box-shadow: 0 6px 20px rgba(217, 0, 0, 0.4);
}

/* About Section */
.about-img-container {
    position: relative;
    width: 100%;
    height: 500px;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    border: 3px solid var(--primary-color);
}

.about-img {
    width: 100%;
    height: 100%;
    background: url('../images/about.jpeg') center/cover no-repeat;
    transition: transform 0.3s ease;
}

.about-img:hover {
    transform: scale(1.05);
}

/* Add a red glow effect to match the neon theme */
.about-img-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    box-shadow: 0 0 20px rgba(217, 0, 0, 0.3);
    border-radius: 10px;
    pointer-events: none;
}

/* Services Section */
.service-card {
    background-color: var(--card-bg);
    color: var(--text-gray);
    border-radius: 8px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 25px rgba(217, 0, 0, 0.2);
}

.service-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background-color: var(--primary-color);
    color: var(--light-color);
    border-radius: 50%;
    margin-bottom: 20px;
    font-size: 2rem;
}

.service-card h3 {
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.service-card .price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-top: 15px;
}

.service-card p {
    color: var(--text-gray);
}

/* Gallery Section - Optimized */
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--media-radius-md, 10px);
    box-shadow: var(--media-shadow-sm, 0 4px 8px rgba(0, 0, 0, 0.1));
    transition: transform var(--media-transition-normal, 0.3s ease);
    aspect-ratio: 4/5;
    min-height: 300px;
    contain: layout style paint;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--media-shadow-md, 0 8px 16px rgba(0, 0, 0, 0.2));
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center top;
    transition: transform var(--media-transition-normal, 0.3s ease);
    will-change: transform;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

/* Optimized lazy loading */
.gallery-lazy-image {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.gallery-lazy-image.loaded {
    opacity: 1;
}

.gallery-lazy-image.loading {
    opacity: 0.7;
    filter: blur(2px);
}

/* My Work Carousel Section */
.work-carousel-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem 0;
}

.work-carousel-card {
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    max-width: 600px;
    width: 100%;
    border: 2px solid var(--primary-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.work-carousel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(220, 53, 69, 0.2);
}

.carousel-image-container {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden;
}

.carousel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.8s ease-in-out, transform 0.8s ease;
}

.carousel-image.fade-out {
    opacity: 0;
    transform: scale(1.05);
}

.carousel-image.fade-in {
    opacity: 1;
    transform: scale(1);
}

.carousel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom,
            rgba(0, 0, 0, 0.1) 0%,
            rgba(0, 0, 0, 0) 30%,
            rgba(0, 0, 0, 0) 70%,
            rgba(0, 0, 0, 0.7) 100%);
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 1rem;
}

.carousel-counter {
    background: rgba(220, 53, 69, 0.9);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.carousel-caption {
    padding: 1.5rem;
    text-align: center;
    background: linear-gradient(145deg, #1a1a1a, #2a2a2a);
    color: white;
}

.carousel-caption h5 {
    color: var(--primary-color);
    font-weight: bold;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.carousel-caption p {
    color: #ccc;
    margin: 0;
    font-size: 0.95rem;
}

/* Responsive adjustments for carousel */
@media (max-width: 768px) {
    .carousel-image-container {
        height: 300px;
    }

    .work-carousel-card {
        margin: 0 1rem;
    }

    .carousel-caption {
        padding: 1rem;
    }

    .carousel-caption h5 {
        font-size: 1.1rem;
    }

    .carousel-caption p {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .carousel-image-container {
        height: 250px;
    }

    .carousel-counter {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* Testimonials Section */
.testimonial-card {
    background-color: var(--card-bg);
    color: var(--text-gray);
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    height: 100%;
}

.testimonial-content {
    color: var(--text-gray);
    margin-bottom: 20px;
    font-style: italic;
}

.testimonial-info h4 {
    color: var(--text-gray);
    margin-bottom: 5px;
}

.stars {
    color: var(--primary-color);
}

/* Contact Section */
.contact-info {
    padding: 30px;
    background-color: var(--card-bg);
    color: var(--text-gray);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    height: 100%;
}

.contact-info i {
    color: var(--primary-color);
    margin-right: 10px;
}

/* Social Icons */
.social-icons {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background-color: var(--primary-color);
    color: var(--light-color);
    border-radius: 50%;
    font-size: 1.4rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    position: relative;
}

.social-icon:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.social-icon.facebook {
    background-color: #1877f2;
}

.social-icon.instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.social-icon.youtube {
    background-color: #FF0000;
}

.social-icon.youtube:hover {
    background-color: #FF3333;
}

.social-icon::after {
    content: attr(aria-label);
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.social-icon:hover::after {
    opacity: 1;
}

.contact-form {
    padding: 30px;
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    height: 100%;
    color: var(--text-gray);
}

/* Form controls */
.form-control {
    background-color: var(--dark-gray);
    border: 1px solid #333;
    color: var(--text-gray);
}

.form-control:focus {
    background-color: var(--dark-gray);
    border-color: var(--primary-color);
    color: var(--text-gray);
    box-shadow: 0 0 0 0.25rem rgba(217, 0, 0, 0.25);
}

.form-control::placeholder {
    color: #999;
}

/* Map Section */
.map-container {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: var(--light-color);
    padding: 30px 0;
}

footer h3 {
    color: var(--light-color);
    margin-bottom: 10px;
}

footer .fa-heart {
    color: var(--primary-color);
}

/* Media Queries */
@media (max-width: 1200px) {
    .hero h1 {
        font-size: 4rem;
    }

    .hero h2 {
        font-size: 2.2rem;
    }

    .service-card {
        padding: 25px 15px;
    }
}

@media (max-width: 992px) {
    .hero h1 {
        font-size: 3.5rem;
    }

    .hero h2 {
        font-size: 2rem;
    }

    .navbar-brand {
        font-size: 1.5rem;
    }

    .about-img-container {
        height: 400px;
    }

    .video-wrapper {
        padding-top: 75%;
        /* Adjust aspect ratio for tablets */
    }
}

@media (max-width: 768px) {
    .hero {
        min-height: 500px;
        padding-top: 100px;
    }

    .hero h1 {
        font-size: 2.8rem;
    }

    .hero h2 {
        font-size: 1.6rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .navbar-logo {
        height: 50px;
        width: 50px;
    }

    .gallery-item {
        /* Remove fixed height on tablets, use aspect ratio instead */
        aspect-ratio: 3/4;
        min-height: 280px;
        max-height: 350px;
    }

    .gallery-item img {
        /* Ensure better centering on tablet */
        object-position: center 20%;
    }

    .contact-info,
    .contact-form {
        margin-bottom: 30px;
        padding: 20px;
    }

    .social-icons {
        justify-content: center;
    }

    .footer-logo {
        height: 60px;
        width: 60px;
    }

    /* Improve form elements on mobile */
    .form-control,
    select.form-control {
        height: 50px;
        font-size: 16px;
        /* Prevent zoom on iOS */
    }

    textarea.form-control {
        height: auto;
    }

    .rotating-gallery {
        height: 250px;
        max-width: 95vw;
    }

    .gallery-arrow {
        width: 36px;
        height: 36px;
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .hero {
        min-height: 400px;
        padding-top: 80px;
    }

    .hero h1 {
        font-size: 2.2rem;
    }

    .hero h2 {
        font-size: 1.3rem;
    }

    .hero .btn-danger {
        padding: 10px 20px;
        font-size: 1rem;
    }

    /* Gallery mobile optimization - center and full width */
    #gallery .row {
        margin: 0;
        justify-content: center;
    }

    #gallery .col-lg-4.col-md-6.col-sm-6 {
        /* Override Bootstrap classes for mobile */
        flex: 0 0 100%;
        max-width: 100%;
        padding: 0 15px;
        margin-bottom: 20px;
    }

    .gallery-item {
        /* Optimized for mobile viewing */
        aspect-ratio: 4/5;
        min-height: 280px;
        max-height: 350px;
        margin: 0 auto;
        width: 100%;
        max-width: 300px;
        /* Prevent images from being too wide on larger mobile screens */
    }

    .gallery-item img {
        /* Better positioning for mobile - focus on face area */
        object-position: center 15%;
        border-radius: 10px;
        width: 100%;
        height: 100%;
    }

    .video-wrapper {
        padding-top: 100%;
        /* Square aspect ratio for mobile */
    }

    .service-card {
        margin-bottom: 20px;
    }

    .testimonial-card {
        padding: 15px;
    }

    /* Adjust spacing for mobile */
    .py-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    /* Make buttons more touch-friendly */
    .btn {
        padding: 12px 20px;
        min-height: 44px;
        /* Minimum touch target size */
    }

    .rotating-gallery {
        height: 180px;
    }

    .gallery-arrow {
        width: 28px;
        height: 28px;
        font-size: 1.1rem;
    }
}

/* Handle very small devices */
@media (max-width: 360px) {
    .hero h1 {
        font-size: 2rem;
    }

    .hero h2 {
        font-size: 1.1rem;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }

    .section-title {
        font-size: 1.8rem;
    }
}

/* Handle landscape orientation */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero h2 {
        font-size: 1.5rem;
    }
}

/* Improve touch targets on mobile */
@media (hover: none) {

    .nav-link,
    .btn,
    .social-icon {
        cursor: default;
    }

    .video-player::-webkit-media-controls {
        transform: scale(1.2);
        transform-origin: center bottom;
    }
}

/* ===== MOBILE TOUCH INTERACTIONS STYLES ===== */

/* Touch-friendly button enhancements */
.btn, .social-icon, .gallery-arrow, .engagement-btn {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
    user-select: none;
    -webkit-user-select: none;
    -webkit-tap-highlight-color: transparent;
}

/* Touch active state */
.touch-active {
    transform: scale(0.95) !important;
    opacity: 0.8 !important;
    transition: all 0.1s ease !important;
}

/* Ripple effect animation */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Pull to refresh indicator */
.pull-to-refresh-indicator {
    position: absolute;
    top: -80px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1000;
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 500;
}

.pull-to-refresh-indicator .pull-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.3s ease;
}

.pull-to-refresh-indicator.ready .pull-icon {
    background: var(--secondary-color);
    transform: rotate(180deg);
}

.pull-to-refresh-indicator.loading .pull-icon {
    background: var(--secondary-color);
}

/* Swipe feedback */
.swipe-feedback {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    z-index: 9999;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.swipe-feedback.show {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
}

.swipe-feedback i {
    font-size: 16px;
    color: var(--primary-color);
}

/* Zoom feedback */
.zoom-feedback {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    z-index: 9999;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.zoom-feedback.show {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
}

.zoom-feedback i {
    color: var(--primary-color);
}

/* Enhanced touch targets for engagement buttons */
.engagement-btn {
    padding: 12px 16px;
    border-radius: 25px;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.engagement-btn:active {
    transform: scale(0.95);
}

/* Swipe indicators for galleries */
.rotating-gallery {
    position: relative;
    overflow: hidden;
}

.rotating-gallery::before,
.rotating-gallery::after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 60px;
    background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
    z-index: 5;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.rotating-gallery::before {
    left: 0;
}

.rotating-gallery::after {
    right: 0;
    background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
}

.rotating-gallery:hover::before,
.rotating-gallery:hover::after {
    opacity: 1;
}

/* Touch-friendly modal controls */
.modal .btn-close {
    min-width: 44px;
    min-height: 44px;
    padding: 12px;
    opacity: 0.8;
}

.modal .modal-header .btn {
    min-height: 44px;
    padding: 8px 16px;
}

/* Enhanced gallery arrows for touch */
.gallery-arrow {
    min-width: 44px;
    min-height: 44px;
    font-size: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
}

.gallery-arrow:active {
    transform: translateY(-50%) scale(0.9);
    background: rgba(217, 0, 0, 1);
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
    /* Larger touch targets */
    .btn {
        min-height: 48px;
        padding: 12px 20px;
        font-size: 16px;
    }
    
    .social-icon {
        width: 48px;
        height: 48px;
        font-size: 18px;
    }
    
    .gallery-arrow {
        width: 48px;
        height: 48px;
        font-size: 20px;
    }
    
    /* Enhanced engagement buttons */
    .engagement-btn {
        min-height: 44px;
        padding: 10px 16px;
        margin: 4px;
    }
    
    /* Better spacing for touch */
    .social-stats span {
        padding: 8px 12px;
        margin: 4px;
        border-radius: 20px;
        min-height: 40px;
        display: flex;
        align-items: center;
    }
    
    /* Improved modal navigation */
    .modal .btn {
        min-height: 48px;
        font-size: 16px;
    }
    
    /* Better thumbnail navigation */
    .thumbnail-item {
        min-width: 60px;
        min-height: 60px;
        margin: 4px;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .thumbnail-item:active {
        transform: scale(0.95);
    }
    
    .thumbnail-item.active {
        border: 3px solid var(--primary-color);
        box-shadow: 0 0 10px rgba(217, 0, 0, 0.5);
    }
}

@media (max-width: 576px) {
    /* Even larger touch targets for small screens */
    .btn {
        min-height: 50px;
        font-size: 16px;
    }
    
    .gallery-arrow {
        width: 50px;
        height: 50px;
        font-size: 22px;
    }
    
    /* Improved pull-to-refresh for small screens */
    .pull-to-refresh-indicator {
        top: -70px;
    }
    
    .pull-to-refresh-indicator .pull-icon {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }
    
    /* Better swipe feedback positioning */
    .swipe-feedback {
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .zoom-feedback {
        width: 50px;
        height: 50px;
        font-size: 18px;
    }
}

/* Prevent text selection during touch interactions */
.posts-feed,
.rotating-gallery,
.modal-content {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Allow text selection for content areas */
.card-text,
.modal-caption,
.comment-text {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* Smooth scrolling for mobile */
html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Optimize touch scrolling */
.posts-feed,
.modal-body {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
}

/* Prevent zoom on input focus (iOS) */
@media screen and (max-width: 768px) {
    input[type="text"],
    input[type="email"],
    input[type="password"],
    textarea,
    select {
        font-size: 16px !important;
    }
}

/* Fix iOS form elements */
@supports (-webkit-touch-callout: none) {

    input,
    select,
    textarea {
        font-size: 16px !important;
    }

    .form-control {
        -webkit-appearance: none;
        border-radius: 8px;
    }
}

/* Navbar Logo */
.navbar-logo {
    height: 60px;
    width: 60px;
    object-fit: contain;
    border-radius: 0;
    /* Remove border radius for the clock logo */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.navbar-logo:hover {
    transform: scale(1.05);
}

/* Adjust navbar padding to accommodate larger logo */
.navbar {
    padding: 0.8rem 1rem;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Footer Logo */
.footer-logo {
    height: 80px;
    width: 80px;
    object-fit: contain;
    border-radius: 0;
    /* Remove border radius for the clock logo */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.footer-logo:hover {
    transform: scale(1.05);
}

/* Override any Bootstrap text colors */
.text-muted {
    color: #999 !important;
}

.lead {
    color: var(--text-gray);
}

p {
    color: var(--text-gray);
}

/* Section backgrounds */
section {
    background-color: var(--gray-color);
}

section.bg-light {
    background-color: var(--dark-gray) !important;
}

/* Add subtle borders to separate sections */
section:not(:last-child) {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Video Gallery Section - Optimized (uses media-hub classes) */
.video-card {
    /* Inherits from .media-card in media-hub.css */
    background-color: var(--card-bg);
    border-radius: var(--media-radius-sm, 8px);
    overflow: hidden;
    box-shadow: var(--media-shadow-md, 0 4px 8px rgba(0, 0, 0, 0.2));
    transition: transform var(--media-transition-normal, 0.3s ease), box-shadow var(--media-transition-normal, 0.3s ease);
    contain: layout style paint;
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--media-shadow-lg, 0 8px 16px rgba(217, 0, 0, 0.2));
}

.video-wrapper {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    background-color: #000;
    overflow: hidden;
}

.video-player {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border: none;
    will-change: transform;
}

.video-info {
    padding: var(--media-spacing-md, 1.5rem);
}

.video-info h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--text-gray);
    font-family: 'Oswald', sans-serif;
}

.video-info p {
    color: var(--text-gray);
    margin-bottom: 0;
    font-size: 0.9rem;
}

/* Optimized video player controls */
.video-player::-webkit-media-controls {
    background-color: rgba(0, 0, 0, 0.7);
}

.video-player::-webkit-media-controls-panel {
    background-color: rgba(0, 0, 0, 0.7);
}

.video-player::-webkit-media-controls-play-button {
    background-color: var(--primary-color);
    border-radius: 50%;
}

/* Add video section to navigation */
.navbar-nav .nav-link[href="#videos"] {
    position: relative;
}

.navbar-nav .nav-link[href="#videos"]::after {
    content: 'NEW';
    position: absolute;
    top: -8px;
    right: -20px;
    font-size: 0.6rem;
    background-color: var(--primary-color);
    color: white;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

/* Rotating Gallery Styles - Optimized (uses media-hub classes) */
.rotating-gallery {
    /* Inherits from .media-card in media-hub.css */
    position: relative;
    width: 100%;
    max-width: 600px;
    height: 400px;
    margin: 0 auto 2rem auto;
    background: var(--media-card-bg, #181818);
    border-radius: var(--media-radius-md, 15px);
    box-shadow: var(--media-shadow-primary, 0 10px 30px rgba(217, 0, 0, 0.3));
    overflow: hidden;
    contain: layout style paint;
}

.rotating-gallery-card {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: var(--media-radius-md, 15px);
    overflow: hidden;
    background-color: var(--media-card-bg, #181818);
}

.gallery-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity var(--media-transition-slow, 1s ease-in-out);
    border-radius: var(--media-radius-md, 15px);
    z-index: 1;
    will-change: opacity;
}

.gallery-image.active {
    opacity: 1;
    z-index: 10;
}

.gallery-arrow {
    /* Inherits from .media-nav-btn in media-hub.css */
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(217, 0, 0, 0.85);
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    font-size: 1.5rem;
    cursor: pointer;
    z-index: 20;
    box-shadow: var(--media-shadow-sm, 0 2px 8px rgba(0, 0, 0, 0.2));
    transition: all var(--media-transition-fast, 0.2s ease);
    outline: none;
}

.gallery-arrow.left {
    left: 16px;
}

.gallery-arrow.right {
    right: 16px;
}

.gallery-arrow:hover,
.gallery-arrow:focus {
    background: var(--media-primary-hover, #b30000);
    transform: translateY(-50%) scale(1.08);
}

@media (max-width: 480px) {

    /* Gallery improvements for medium mobile devices */
    #gallery .col-lg-4.col-md-6.col-sm-6 {
        padding: 0 10px;
        margin-bottom: 18px;
    }

    .gallery-item {
        max-width: 320px;
        /* Slightly larger for medium mobile screens */
        min-height: 260px;
        max-height: 330px;
    }

    .rotating-gallery {
        height: 180px;
    }

    .gallery-arrow {
        width: 28px;
        height: 28px;
        font-size: 1.1rem;
    }
}

/* Extra small mobile devices (320px and below) */
@media (max-width: 320px) {
    #gallery .container {
        padding-left: 10px;
        padding-right: 10px;
    }

    #gallery .col-lg-4.col-md-6.col-sm-6 {
        padding: 0 5px;
        margin-bottom: 15px;
    }

    .gallery-item {
        aspect-ratio: 4/5;
        /* Maintain consistent aspect ratio */
        min-height: 240px;
        max-height: 280px;
        max-width: 280px;
        /* Slightly smaller for very small screens */
    }

    .gallery-item img {
        object-position: center 10%;
        /* Focus even more on the top for tiny screens */
    }
}

/* Alert Messages Styling */
.alert {
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    font-weight: 500;
}

.alert-success {
    background-color: #28a745 !important;
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert h4 {
    margin-bottom: 15px;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert p {
    margin-bottom: 10px;
    line-height: 1.5;
}

.alert small {
    opacity: 0.9;
}

.alert i {
    margin-right: 5px;
}

/* Community Section */
.community-hub-wrapper {
    background-color: var(--card-bg);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 2px solid var(--primary-color);
    min-height: 400px;
}

.community-feature-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(217, 0, 0, 0.2);
}

.community-feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(217, 0, 0, 0.3);
}

.community-feature-card h4 {
    color: var(--light-color);
    margin-bottom: 1rem;
}

.community-feature-card p {
    color: var(--text-gray);
    margin-bottom: 0;
}

.feature-icon {
    opacity: 0.8;
}

/* Community Hub Loading State */
.community-hub-wrapper .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Community Navigation Integration */
.nav-link[href="#community"] {
    position: relative;
}

.nav-link[href="#community"]::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link[href="#community"]:hover::after,
.nav-link[href="#community"].active::after {
    width: 80%;
}

/* Community Hub Content */
.community-hub-content {
    width: 100%;
}

.community-navigation .btn-group {
    border-radius: 8px;
    overflow: hidden;
}

.community-navigation .btn {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: transparent;
    padding: 12px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.community-navigation .btn:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.community-navigation .btn.active {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 8px rgba(217, 0, 0, 0.3);
}

.community-view {
    min-height: 300px;
    padding: 1.5rem;
    background-color: rgba(26, 26, 26, 0.5);
    border-radius: 10px;
    border: 1px solid rgba(217, 0, 0, 0.1);
}

.community-view h2 {
    color: var(--light-color);
    margin-bottom: 1.5rem;
}

.community-view p {
    color: var(--text-gray);
}

/* Coming Soon Styling */
.coming-soon {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-gray);
}

.coming-soon i {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    opacity: 0.7;
}

.coming-soon h3 {
    color: var(--light-color);
    margin-bottom: 1rem;
}

/* Responsive adjustments for community */
@media (max-width: 768px) {
    .community-navigation .btn {
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .community-hub-wrapper {
        padding: 1rem;
    }

    .community-view {
        padding: 1rem;
        min-height: 250px;
    }
}

/* ===== ENHANCED SOCIAL MEDIA STYLES (uses media-hub classes) ===== */

/* Social Media Section Background - Optimized */
#social-feed {
    /* Inherits from .media-hub in media-hub.css */
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
    position: relative;
    overflow: hidden;
}

#social-feed::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 50%, rgba(217, 0, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(217, 0, 0, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

/* Enhanced Profile Header */
.social-profile-header {
    background: linear-gradient(145deg, rgba(26, 26, 26, 0.95), rgba(40, 40, 40, 0.95));
    border: 1px solid rgba(217, 0, 0, 0.2);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(217, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(217, 0, 0, 0.1), transparent);
    transition: left 0.5s ease;
}

.social-profile-header:hover::before {
    left: 100%;
}

.social-profile-header:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.5),
        0 0 30px rgba(217, 0, 0, 0.2);
}

/* Enhanced Profile Picture */
.profile-pic {
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-color);
    box-shadow: 0 0 20px rgba(217, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.profile-pic:hover {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(217, 0, 0, 0.5);
}

/* Enhanced Social Stats */
.social-stats {
    display: flex;
    gap: 2rem;
    margin-top: 1rem;
}

.social-stats span {
    font-size: 0.95rem;
    color: var(--text-gray);
    transition: color 0.3s ease;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.social-stats span:hover {
    color: var(--primary-color);
    background-color: rgba(217, 0, 0, 0.1);
    transform: translateY(-2px);
}

.social-stats span strong {
    color: var(--light-color);
    font-weight: 700;
}

/* Enhanced Follow Button */
.social-profile-header .btn {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-profile-header .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.social-profile-header .btn:hover::before {
    left: 100%;
}

.social-profile-header .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(217, 0, 0, 0.4);
}

/* Enhanced Login Section */
#login-section {
    background: linear-gradient(145deg, rgba(26, 26, 26, 0.9), rgba(40, 40, 40, 0.9));
    border: 1px solid rgba(217, 0, 0, 0.3);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

#login-section:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.4);
}

#login-section h3 {
    color: var(--light-color);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

#login-section p {
    color: var(--text-gray);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

/* Enhanced Create Post Section */
.create-post .card {
    background: linear-gradient(145deg, rgba(26, 26, 26, 0.95), rgba(40, 40, 40, 0.95));
    border: 1px solid rgba(217, 0, 0, 0.2);
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.create-post .card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.create-post .form-control {
    background-color: rgba(42, 42, 42, 0.8);
    border: 1px solid rgba(217, 0, 0, 0.2);
    border-radius: 25px;
    padding: 1rem 1.5rem;
    color: var(--text-gray);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.create-post .form-control:focus {
    background-color: rgba(42, 42, 42, 1);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(217, 0, 0, 0.25);
    transform: scale(1.02);
}

.create-post .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Enhanced Post Actions in Create Post */
.create-post .post-actions .btn {
    border-radius: 15px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.create-post .post-actions .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.create-post .post-actions .btn:hover::before {
    left: 100%;
}

.create-post .post-actions .btn:hover {
    transform: translateY(-2px);
}

/* Enhanced Social Posts - Optimized (uses media-hub classes) */
.social-post {
    /* Inherits from .media-card in media-hub.css */
    background: linear-gradient(145deg, rgba(26, 26, 26, 0.95), rgba(35, 35, 35, 0.95));
    border: 1px solid rgba(217, 0, 0, 0.15);
    border-radius: var(--media-radius-lg, 20px);
    box-shadow: var(--media-shadow-lg, 0 8px 30px rgba(0, 0, 0, 0.3));
    backdrop-filter: blur(10px);
    transition: all var(--media-transition-normal, 0.3s ease);
    overflow: hidden;
    position: relative;
    contain: layout style paint;
}

.social-post::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), #ff4444, var(--primary-color));
    opacity: 0;
    transition: opacity var(--media-transition-normal, 0.3s ease);
}

.social-post:hover::before {
    opacity: 1;
}

.social-post:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.4), 0 0 30px rgba(217, 0, 0, 0.1);
    border-color: rgba(217, 0, 0, 0.3);
}

/* Enhanced Post Header */
.post-header {
    background: transparent;
    padding: 1.5rem 1.5rem 1rem 1.5rem;
}

.post-header .user-avatar {
    border: 2px solid var(--primary-color);
    box-shadow: 0 0 15px rgba(217, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.post-header .user-avatar:hover {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(217, 0, 0, 0.5);
}

.post-header h6 {
    color: var(--light-color);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.post-header small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
}

/* Enhanced Post Content */
.social-post .card-body {
    padding: 0 1.5rem 1.5rem 1.5rem;
}

.social-post .card-text {
    color: var(--text-gray);
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* Enhanced Post Images */
.post-image {
    width: 100%;
    max-height: 500px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

.post-image:hover {
    transform: scale(1.02);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

/* Enhanced Post Stats */
.post-stats {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 1rem;
}

.post-stats span {
    transition: all 0.3s ease;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 8px;
}

.post-stats span:hover {
    color: var(--primary-color);
    background-color: rgba(217, 0, 0, 0.1);
    transform: translateY(-1px);
}

.post-stats .fas.fa-heart {
    color: var(--primary-color);
}

/* Enhanced Post Actions */
.post-actions {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
    display: flex;
    justify-content: space-around;
}

.post-actions .btn {
    border: none;
    background: transparent;
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
    font-weight: 500;
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.post-actions .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(217, 0, 0, 0.1), transparent);
    transition: left 0.5s ease;
}

.post-actions .btn:hover::before {
    left: 100%;
}

.post-actions .btn:hover {
    color: var(--primary-color);
    background-color: rgba(217, 0, 0, 0.1);
    transform: translateY(-2px);
}

.post-actions .btn.liked {
    color: var(--primary-color);
    background-color: rgba(217, 0, 0, 0.1);
}

.post-actions .btn.liked .far.fa-heart::before {
    content: "\f004";
    font-weight: 900;
}

/* Enhanced Load More Button */
.posts-feed .btn-outline-danger {
    border-radius: 25px;
    padding: 1rem 3rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.posts-feed .btn-outline-danger::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    transition: left 0.3s ease;
    z-index: -1;
}

.posts-feed .btn-outline-danger:hover::before {
    left: 0;
}

.posts-feed .btn-outline-danger:hover {
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(217, 0, 0, 0.4);
}

/* Dropdown Menu Enhancements */
.dropdown-menu-dark {
    background-color: rgba(26, 26, 26, 0.95);
    border: 1px solid rgba(217, 0, 0, 0.2);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

.dropdown-menu-dark .dropdown-item {
    color: var(--text-gray);
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    margin: 0.25rem;
    transition: all 0.3s ease;
}

.dropdown-menu-dark .dropdown-item:hover {
    background-color: rgba(217, 0, 0, 0.1);
    color: var(--primary-color);
    transform: translateX(5px);
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes heartBeat {
    0% {
        transform: scale(1);
    }

    14% {
        transform: scale(1.3);
    }

    28% {
        transform: scale(1);
    }

    42% {
        transform: scale(1.3);
    }

    70% {
        transform: scale(1);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

.heart-beat {
    animation: heartBeat 1.3s ease-in-out;
}

/* Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }

    100% {
        background-position: 200% 0;
    }
}

/* Enhanced Profile Header Components */
.profile-pic-container {
    position: relative;
}

.verified-badge {
    bottom: 5px;
    right: 15px;
    background: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.verified-badge .fa-check-circle {
    color: var(--primary-color);
    font-size: 16px;
}

.badge.bg-danger {
    background-color: var(--primary-color) !important;
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.profile-bio {
    color: var(--text-gray);
    line-height: 1.5;
}

.profile-bio p {
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.hashtags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.hashtag {
    color: var(--primary-color);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0.2rem 0.5rem;
    border-radius: 8px;
    background-color: rgba(217, 0, 0, 0.1);
}

.hashtag:hover {
    background-color: rgba(217, 0, 0, 0.2);
    transform: translateY(-1px);
}

.profile-social-links {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.social-link.instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.social-link.youtube {
    background-color: #FF0000;
}

.social-link.facebook {
    background-color: #1877f2;
}

.social-link.website {
    background-color: var(--primary-color);
}

.social-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    color: white;
}

.business-status {
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-indicator {
    width: 8px;
    height: 8px;
    background-color: #28a745;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-text {
    color: #28a745;
    font-weight: 500;
    font-size: 0.9rem;
}

.business-status.closed {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: rgba(220, 53, 69, 0.3);
}

.business-status.closed .status-indicator {
    background-color: #dc3545;
}

.business-status.closed .status-text {
    color: #dc3545;
}

/* Enhanced Follow Button States */
#follow-btn[data-following="true"] {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

#follow-btn[data-following="true"]:hover {
    background-color: #b30000;
    border-color: #b30000;
}

#follow-btn[data-following="true"] .fa-plus::before {
    content: "\f00c";
}

/* Profile Header Dropdown */
.social-profile-header .dropdown-toggle {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: transparent;
    color: var(--text-gray);
    transition: all 0.3s ease;
}

.social-profile-header .dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Enhanced Comment System */
.comments-section {
    margin-top: 1rem;
    padding-top: 1rem;
}

.comment {
    margin-bottom: 1rem;
}

.user-avatar-sm {
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(217, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.user-avatar-sm:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.comment-bubble {
    background-color: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.comment-bubble:hover {
    background-color: rgba(45, 45, 45, 0.9);
    border-color: rgba(217, 0, 0, 0.2);
}

.comment-bubble strong {
    color: var(--light-color);
    font-weight: 600;
}

.comment-bubble p {
    color: var(--text-gray);
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.comment-actions {
    padding-left: 1rem;
}

.comment-actions .btn {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    transition: all 0.3s ease;
}

.comment-actions .btn:hover {
    color: var(--primary-color);
    transform: translateY(-1px);
}

.comment-like-btn.heart-beat {
    animation: heartBeat 0.6s ease-in-out;
}

/* Add Comment Form */
.add-comment-form .input-group {
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.add-comment-form .form-control {
    background-color: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-gray);
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    border-radius: 25px 0 0 25px;
}

.add-comment-form .form-control:focus {
    background-color: rgba(45, 45, 45, 0.9);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(217, 0, 0, 0.25);
    color: var(--light-color);
}

.add-comment-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.submit-comment-btn {
    border-radius: 0 25px 25px 0;
    padding: 0.75rem 1rem;
    border: 1px solid var(--primary-color);
    background-color: var(--primary-color);
    color: white;
    transition: all 0.3s ease;
}

.submit-comment-btn:hover {
    background-color: #b30000;
    border-color: #b30000;
    transform: scale(1.05);
}

/* Enhanced Hashtags in Posts */
.social-post .hashtag {
    color: var(--primary-color);
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0.1rem 0.3rem;
    border-radius: 4px;
    background-color: rgba(217, 0, 0, 0.1);
    font-weight: 500;
}

.social-post .hashtag:hover {
    background-color: rgba(217, 0, 0, 0.2);
    transform: translateY(-1px);
}

/* Save Post Styling */
.save-post.saved {
    color: #ffc107 !important;
}

.save-post.saved i {
    color: #ffc107 !important;
}

/* Image Modal Enhancements */
.modal-content.bg-dark {
    background-color: rgba(26, 26, 26, 0.95) !important;
    border: 1px solid rgba(217, 0, 0, 0.3);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
}

.modal-header.border-0 {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.modal-footer.border-0 {
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

#modalImage {
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    transition: transform 0.3s ease;
}

#modalImage:hover {
    transform: scale(1.02);
}

/* Share Modal Enhancements */
.share-platform {
    border-radius: 15px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.share-platform::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.share-platform:hover::before {
    left: 100%;
}

.share-platform:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Floating Heart Animation */
@keyframes floatHeart {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-50px) scale(1.5);
        opacity: 0;
    }
}

/* Enhanced Post Action States */
.post-actions .btn.liked {
    color: var(--primary-color) !important;
    background-color: rgba(217, 0, 0, 0.1);
}

.post-actions .btn.liked i {
    color: var(--primary-color);
}

/* Loading States for Engagement */
.loading-engagement {
    opacity: 0.6;
    pointer-events: none;
}

.loading-engagement::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Dropdown Menu Items */
.dropdown-menu-dark .dropdown-item i {
    width: 20px;
    text-align: center;
    opacity: 0.8;
}

.dropdown-menu-dark .dropdown-item:hover i {
    opacity: 1;
    color: var(--primary-color);
}

/* Advanced Comment System Enhancements */
.comment-btn.active {
    color: var(--primary-color) !important;
    background-color: rgba(217, 0, 0, 0.1);
}

.comment-btn.active i {
    color: var(--primary-color);
}

/* Reply System Styles */
.reply {
    border-left: 2px solid rgba(217, 0, 0, 0.3);
    padding-left: 0.5rem;
    margin-left: 1rem;
}

.reply-bubble {
    background-color: rgba(35, 35, 35, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.reply-bubble:hover {
    background-color: rgba(40, 40, 40, 0.9);
    border-color: rgba(217, 0, 0, 0.15);
}

.reply-form {
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 10px;
    padding: 0.75rem;
    border: 1px solid rgba(217, 0, 0, 0.2);
}

.reply-input {
    background-color: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-gray);
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

.reply-input:focus {
    background-color: rgba(45, 45, 45, 0.9);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(217, 0, 0, 0.2);
    color: var(--light-color);
}

.submit-reply-btn {
    border-radius: 20px;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
}

/* Real-time Comment Animations */
.real-time-comment {
    border-left: 3px solid var(--primary-color);
    padding-left: 0.5rem;
    background-color: rgba(217, 0, 0, 0.05);
    border-radius: 0 10px 10px 0;
    animation: newCommentGlow 2s ease-out;
}

@keyframes newCommentGlow {
    0% {
        background-color: rgba(217, 0, 0, 0.2);
        box-shadow: 0 0 20px rgba(217, 0, 0, 0.3);
    }
    100% {
        background-color: rgba(217, 0, 0, 0.05);
        box-shadow: none;
    }
}

/* Comment Context Menu */
.comment-context-menu {
    backdrop-filter: blur(10px);
    animation: contextMenuFadeIn 0.2s ease-out;
}

.comment-context-menu .btn:hover {
    background-color: rgba(217, 0, 0, 0.1);
    color: var(--primary-color) !important;
}

@keyframes contextMenuFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Comment Search */
.comment-search {
    background-color: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-gray);
    border-radius: 20px;
    transition: all 0.3s ease;
}

.comment-search:focus {
    background-color: rgba(45, 45, 45, 0.9);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(217, 0, 0, 0.2);
    color: var(--light-color);
}

.comment-search::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* Enhanced Comment Actions */
.comment-actions .btn:hover,
.reply-actions .btn:hover {
    color: var(--primary-color) !important;
    background-color: rgba(217, 0, 0, 0.1);
    border-radius: 8px;
    transform: translateY(-1px);
}

/* Staggered Animation Classes */
.stagger-animation {
    animation: staggerFadeIn 0.4s ease-out forwards;
}

@keyframes staggerFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mini Heart Animation */
@keyframes miniHeart {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-20px) scale(1.2);
        opacity: 0;
    }
}

/* Comment Loading States */
.comment-loading {
    opacity: 0.6;
    pointer-events: none;
}

.comment-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    margin: -6px 0 0 -6px;
    border: 2px solid transparent;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Enhanced Reply Threading */
.reply-thread {
    border-left: 2px solid rgba(217, 0, 0, 0.2);
    margin-left: 1rem;
    padding-left: 1rem;
    position: relative;
}

.reply-thread::before {
    content: '';
    position: absolute;
    left: -2px;
    top: 0;
    width: 2px;
    height: 20px;
    background: linear-gradient(to bottom, var(--primary-color), transparent);
}

/* Comment Moderation Indicators */
.comment-reported {
    opacity: 0.5;
    border: 1px solid #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
}

.comment-reported::after {
    content: 'Reported';
    position: absolute;
    top: 5px;
    right: 5px;
    background: #ffc107;
    color: #000;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: bold;
}

/* Responsive Enhancements for Comments */
@media (max-width: 768px) {
    .reply {
        margin-left: 0.5rem;
        padding-left: 0.25rem;
    }
    
    .reply-form {
        padding: 0.5rem;
    }
    
    .comment-context-menu {
        font-size: 0.9rem;
    }
    
    .user-avatar-sm {
        width: 28px !important;
        height: 28px !important;
    }
}

@media (max-width: 576px) {
    .reply {
        margin-left: 0.25rem;
    }
    
    .comment-actions,
    .reply-actions {
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .comment-actions .btn,
    .reply-actions .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

/* ===== ENHANCED POST COMPOSER STYLES ===== */

/* Enhanced Post Composer Card */
.enhanced-post-composer {
    background: linear-gradient(145deg, rgba(26, 26, 26, 0.95), rgba(40, 40, 40, 0.95));
    border: 1px solid rgba(217, 0, 0, 0.2);
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4), 
                0 0 20px rgba(217, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    color: var(--text-gray);
}

.enhanced-post-composer:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.5), 
                0 0 30px rgba(217, 0, 0, 0.15);
}

.enhanced-post-composer .card-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
}

.enhanced-post-composer .card-body {
    padding: 0 1.5rem 1.5rem 1.5rem;
}

/* Enhanced Textarea */
.enhanced-textarea {
    background-color: rgba(40, 40, 40, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    color: var(--text-gray);
    font-size: 1rem;
    line-height: 1.5;
    padding: 1rem 1.25rem;
    resize: none;
    transition: all 0.3s ease;
    min-height: 80px;
}

.enhanced-textarea:focus {
    background-color: rgba(45, 45, 45, 0.9);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(217, 0, 0, 0.25);
    color: var(--light-color);
    transform: scale(1.01);
}

.enhanced-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
    font-style: italic;
}

.post-text-container.focused {
    transform: scale(1.01);
}

/* Textarea Footer */
.textarea-footer {
    padding: 0.5rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
}

.text-formatting-tools .btn {
    color: rgba(255, 255, 255, 0.6);
    padding: 0.25rem 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.text-formatting-tools .btn:hover {
    color: var(--primary-color);
    background-color: rgba(217, 0, 0, 0.1);
    transform: translateY(-1px);
}

.character-count {
    font-size: 0.85rem;
    transition: color 0.3s ease;
}

.character-count.text-warning {
    color: #ffc107 !important;
}

.character-count.text-danger {
    color: #dc3545 !important;
    font-weight: bold;
}

/* Hashtag Suggestions */
.hashtag-suggestions {
    background-color: rgba(30, 30, 30, 0.8);
    border: 1px solid rgba(217, 0, 0, 0.2);
    border-radius: 12px;
    padding: 1rem;
    animation: slideInUp 0.3s ease-out;
}

.suggested-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.suggested-hashtag {
    background-color: rgba(217, 0, 0, 0.1);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(217, 0, 0, 0.3);
}

.suggested-hashtag:hover {
    background-color: rgba(217, 0, 0, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(217, 0, 0, 0.3);
}

/* Media Upload Section */
.media-upload-section {
    background-color: rgba(30, 30, 30, 0.5);
    border: 2px dashed rgba(217, 0, 0, 0.3);
    border-radius: 15px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.upload-area {
    transition: all 0.3s ease;
}

.upload-area.drag-over {
    border-color: var(--primary-color);
    background-color: rgba(217, 0, 0, 0.1);
    transform: scale(1.02);
}

.upload-placeholder {
    border-radius: 10px;
    transition: all 0.3s ease;
}

.upload-placeholder:hover {
    background-color: rgba(40, 40, 40, 0.5);
}

/* Media Preview */
.media-preview {
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: 12px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
}

.media-preview-item {
    border-radius: 10px;
    overflow: hidden;
    background-color: rgba(40, 40, 40, 0.8);
    transition: all 0.3s ease;
}

.media-preview-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.preview-image,
.preview-video {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
}

.remove-media-btn {
    border-radius: 50%;
    width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.file-info {
    padding: 0.5rem;
    text-align: center;
}

/* Enhanced Post Actions */
.enhanced-post-actions {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
    margin-top: 1rem;
}

.media-options .btn {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    margin-right: 0.5rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.media-options .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.media-options .btn:hover::before {
    left: 100%;
}

.media-options .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.post-controls .btn {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.post-controls .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.post-controls .btn-danger:not(:disabled):hover {
    background-color: #b30000;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(217, 0, 0, 0.4);
}

/* Location Section */
.location-section {
    animation: slideInUp 0.3s ease-out;
}

.location-section .input-group-text {
    background-color: rgba(40, 40, 40, 0.8);
    border-color: rgba(255, 255, 255, 0.2);
}

.location-section .form-control {
    background-color: rgba(40, 40, 40, 0.8);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--text-gray);
}

.location-section .form-control:focus {
    background-color: rgba(45, 45, 45, 0.9);
    border-color: var(--primary-color);
    color: var(--light-color);
}

/* Emoji Picker */
.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 0.5rem;
    padding: 0.5rem;
}

.emoji-option {
    font-size: 1.5rem;
    padding: 0.5rem;
    text-align: center;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.emoji-option:hover {
    background-color: rgba(217, 0, 0, 0.1);
    transform: scale(1.2);
}

/* Privacy Dropdown */
.enhanced-post-composer .dropdown-menu {
    background-color: rgba(26, 26, 26, 0.95);
    border: 1px solid rgba(217, 0, 0, 0.2);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

.enhanced-post-composer .dropdown-item {
    color: var(--text-gray);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin: 0.25rem;
    transition: all 0.3s ease;
}

.enhanced-post-composer .dropdown-item:hover {
    background-color: rgba(217, 0, 0, 0.1);
    color: var(--primary-color);
    transform: translateX(5px);
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Enhancements for Post Composer */
@media (max-width: 768px) {
    .enhanced-post-composer .card-header,
    .enhanced-post-composer .card-body {
        padding: 1rem;
    }
    
    .enhanced-post-actions {
        flex-direction: column;
        gap: 1rem;
    }
    
    .media-options {
        display: flex;
        justify-content: space-around;
    }
    
    .media-options .btn {
        margin-right: 0;
        flex: 1;
        margin: 0 0.25rem;
    }
    
    .post-controls {
        display: flex;
        gap: 0.5rem;
    }
    
    .post-controls .btn {
        flex: 1;
    }
    
    .preview-container {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .emoji-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

@media (max-width: 576px) {
    .enhanced-post-composer {
        border-radius: 15px;
        margin: 0 0.5rem;
    }
    
    .text-formatting-tools {
        display: flex;
        gap: 0.25rem;
    }
    
    .suggested-tags {
        justify-content: center;
    }
    
    .suggested-hashtag {
        font-size: 0.8rem;
        padding: 0.2rem 0.6rem;
    }
    
    .preview-container {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.5rem;
    }
    
    .preview-image,
    .preview-video {
        height: 100px;
    }
    
    .emoji-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .emoji-option {
        font-size: 1.2rem;
        padding: 0.4rem;
    }
}

/* ===== ENHANCED MEDIA VIEWER STYLES (uses media-hub classes) ===== */

/* Enhanced Media Viewer Modal - Optimized */
.enhanced-media-viewer {
    /* Inherits from .media-viewer-modal in media-hub.css */
    background: linear-gradient(145deg, rgba(20, 20, 20, 0.98), rgba(30, 30, 30, 0.98));
    border: 1px solid rgba(217, 0, 0, 0.3);
    border-radius: var(--media-radius-lg, 20px);
    box-shadow: var(--media-shadow-lg, 0 20px 60px rgba(0, 0, 0, 0.8)), 
                0 0 30px rgba(217, 0, 0, 0.2);
    backdrop-filter: blur(15px);
    overflow: hidden;
}

.enhanced-media-viewer .modal-header {
    padding: 1.25rem 1.5rem;
    background-color: rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(217, 0, 0, 0.2);
}

.enhanced-media-viewer .modal-footer {
    padding: 1.25rem 1.5rem;
    background-color: rgba(0, 0, 0, 0.3);
    border-top: 1px solid rgba(217, 0, 0, 0.2);
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Media Container - Optimized */
.media-container {
    /* Inherits from .media-viewer-content in media-hub.css */
    position: relative;
    width: 100%;
    height: 70vh;
    max-height: 700px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.8);
    overflow: hidden;
    contain: layout style paint;
}

.image-container,
.video-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform var(--media-transition-normal, 0.3s ease);
}

#modalImage,
#modalVideo {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform var(--media-transition-normal, 0.3s ease);
    box-shadow: var(--media-shadow-lg, 0 10px 30px rgba(0, 0, 0, 0.5));
    will-change: transform;
}

/* Zoom Controls - Optimized */
.zoom-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 10;
}

.zoom-btn {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--media-transition-normal, 0.3s ease);
}

.zoom-btn:hover {
    background-color: rgba(217, 0, 0, 0.7);
    transform: scale(1.1);
}

/* Loading Indicator - Optimized */
.media-loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--media-transition-normal, 0.3s ease);
}

.media-loading-indicator.active {
    opacity: 1;
    pointer-events: auto;
}

/* Thumbnails Navigation */
.thumbnails-container {
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.5);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: thin;
    scrollbar-color: rgba(217, 0, 0, 0.5) rgba(0, 0, 0, 0.3);
}

.thumbnails-container::-webkit-scrollbar {
    height: 6px;
}

.thumbnails-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
}

.thumbnails-container::-webkit-scrollbar-thumb {
    background-color: rgba(217, 0, 0, 0.5);
    border-radius: 10px;
}

.thumbnails-scroller {
    display: inline-flex;
    gap: 10px;
    padding: 5px 0;
}

.thumbnail-item {
    width: 80px;
    height: 60px;
    border-radius: 5px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.thumbnail-item:hover {
    opacity: 1;
    transform: translateY(-2px);
}

.thumbnail-item.active {
    border-color: var(--primary-color);
    opacity: 1;
}

.thumbnail-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Fullscreen Mode */
.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background-color: rgba(0, 0, 0, 0.95);
}

.fullscreen-mode .media-container {
    height: 100vh;
    max-height: none;
}

.fullscreen-mode .media-nav-btn {
    width: 60px;
    height: 60px;
    font-size: 1.8rem;
}

.fullscreen-mode .zoom-controls {
    bottom: 30px;
    right: 30px;
}

.fullscreen-mode .zoom-btn {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

/* Responsive Media Viewer */
@media (max-width: 992px) {
    .media-container {
        height: 50vh;
    }
    
    .media-nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .zoom-controls {
        bottom: 15px;
        right: 15px;
    }
    
    .zoom-btn {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
    
    .thumbnail-item {
        width: 60px;
        height: 45px;
    }
}

@media (max-width: 768px) {
    .media-container {
        height: 40vh;
    }
    
    .media-nav-btn {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
    
    .prev-btn {
        left: 10px;
    }
    
    .next-btn {
        right: 10px;
    }
    
    .zoom-controls {
        bottom: 10px;
        right: 10px;
    }
    
    .zoom-btn {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
    
    .media-info p {
        font-size: 0.9rem;
    }
    
    .media-stats {
        font-size: 0.8rem;
    }
    
    .media-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .thumbnail-item {
        width: 50px;
        height: 40px;
    }
}

@media (max-width: 576px) {
    .media-container {
        height: 35vh;
    }
    
    .media-nav-btn {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
    }
    
    .zoom-controls {
        display: none;
    }
    
    .media-info {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .media-actions .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
    
    .thumbnail-item {
        width: 40px;
        height: 30px;
    }
}

/* ===== PERFORMANCE OPTIMIZATION STYLES (uses media-hub classes) ===== */

/* Progressive Image Loading - Optimized */
.post-image {
    /* Inherits from .media-image in media-hub.css */
    transition: filter var(--media-transition-slow, 0.5s ease-out), transform var(--media-transition-normal, 0.3s ease);
    will-change: filter, transform;
    transform: translateZ(0); /* Force GPU acceleration */
}

.post-image.loading {
    filter: blur(10px);
    animation: pulse-loading 1.5s infinite;
}

.post-image.loaded {
    filter: blur(0);
}

.post-image.error {
    filter: grayscale(100%);
    opacity: 0.7;
}

@keyframes pulse-loading {
    0% { opacity: 0.6; }
    50% { opacity: 0.8; }
    100% { opacity: 0.6; }
}

/* Network Status Indicators - Optimized */
.offline-mode .network-dependent {
    opacity: 0.5;
    pointer-events: none;
}

.offline-mode .offline-message {
    display: block !important;
}

.offline-message {
    display: none;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    text-align: center;
    padding: 10px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    font-weight: bold;
}

/* Low Bandwidth Optimizations */
.low-bandwidth .high-res-image { display: none; }
.low-bandwidth .low-res-image { display: block; }
.low-bandwidth video { display: none; }
.low-bandwidth .video-placeholder { display: flex; }

/* Loading States - Optimized */
.content-loading {
    /* Inherits from .media-loading in media-hub.css */
    position: relative;
    min-height: 100px;
}

.content-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border: 3px solid rgba(217, 0, 0, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Image Error States - Optimized */
.image-error-container {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: var(--media-radius-md, 10px);
    padding: var(--media-spacing-lg, 20px);
    text-align: center;
}

.image-error-container i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

/* Performance Monitoring Toast */
.performance-toast {
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    border-left: 4px solid var(--primary-color);
}

.performance-toast .toast-body {
    display: flex;
    align-items: center;
}

.performance-toast .performance-icon {
    font-size: 1.5rem;
    margin-right: 10px;
}

/* Responsive Performance Optimizations */
@media (max-width: 768px) {
    .performance-heavy { display: none; }
    .mobile-optimized { display: block; }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .social-profile-header {
        padding: 1.5rem;
        border-radius: 15px;
    }
    
    .profile-pic-container .profile-pic {
        width: 70px;
        height: 70px;
    }
    
    .verified-badge {
        width: 18px;
        height: 18px;
        bottom: 3px;
        right: 12px;
    }
    
    .verified-badge .fa-check-circle {
        font-size: 14px;
    }

    .social-stats {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .social-stats span {
        font-size: 0.85rem;
        padding: 0.4rem 0.8rem;
    }
    
    .profile-social-links {
        justify-content: center;
        gap: 0.8rem;
    }
    
    .social-link {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
    
    .hashtags {
        justify-content: center;
    }

    .social-post {
        border-radius: 15px;
        margin-bottom: 1.5rem;
    }

    .post-actions .btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }

    .create-post .form-control {
        padding: 0.8rem 1.2rem;
        font-size: 0.95rem;
    }

    .posts-feed .btn-outline-danger {
        padding: 0.8rem 2rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .social-profile-header {
        padding: 1rem;
    }
    
    .profile-pic-container .profile-pic {
        width: 60px;
        height: 60px;
    }
    
    .verified-badge {
        width: 16px;
        height: 16px;
        bottom: 2px;
        right: 8px;
    }
    
    .verified-badge .fa-check-circle {
        font-size: 12px;
    }

    .social-stats {
        gap: 0.5rem;
    }

    .social-stats span {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
    }
    
    .profile-social-links {
        gap: 0.6rem;
    }
    
    .social-link {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }
    
    .hashtag {
        font-size: 0.8rem;
        padding: 0.15rem 0.4rem;
    }
    
    .business-status {
        padding: 0.75rem;
    }
    
    .status-text {
        font-size: 0.85rem;
    }

    .post-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .post-actions .btn {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }
}/* F
irebase Social Media Enhancements */
.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 15px rgba(217, 0, 0, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 25px rgba(217, 0, 0, 0.6);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 15px rgba(217, 0, 0, 0.3);
    }
}

/* Enhanced social media buttons */
.post-actions .btn-link {
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 8px 12px;
}

.post-actions .btn-link:hover {
    background-color: rgba(217, 0, 0, 0.1);
    transform: translateY(-1px);
}

.post-actions .btn-link.liked {
    color: var(--primary-color) !important;
}

/* Loading states */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Comment section animations */
.comments-section {
    transition: all 0.3s ease;
    overflow: hidden;
}

.comment {
    animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced toast notifications */
.toast {
    backdrop-filter: blur(10px);
    background-color: rgba(26, 26, 26, 0.95) !important;
    border: 1px solid rgba(217, 0, 0, 0.3);
}

.toast-header {
    border-bottom: 1px solid rgba(217, 0, 0, 0.2);
}

/* User avatar enhancements */
.user-avatar, .user-avatar-sm {
    border: 2px solid rgba(217, 0, 0, 0.3);
    transition: border-color 0.3s ease;
}

.user-avatar:hover, .user-avatar-sm:hover {
    border-color: var(--primary-color);
}

/* Post engagement stats */
.post-stats span {
    transition: color 0.3s ease;
    cursor: pointer;
}

.post-stats span:hover {
    color: var(--primary-color) !important;
}

/* Enhanced form controls */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(217, 0, 0, 0.25);
}

/* Real-time update indicators */
.new-content-indicator {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    animation: fadeInDown 0.5s ease;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Error states */
.error-state {
    color: #ff6b6b;
    background-color: rgba(255, 107, 107, 0.1);
    border: 1px solid rgba(255, 107, 107, 0.3);
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
}

/* Success states */
.success-state {
    color: #51cf66;
    background-color: rgba(81, 207, 102, 0.1);
    border: 1px solid rgba(81, 207, 102, 0.3);
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
}
/* ===== SEARCH AND HASHTAG FILTERING STYLES ===== */

.search-container {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-container .input-group {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 25px;
    overflow: hidden;
}

.search-container .input-group-text {
    border-color: var(--primary-color);
    background-color: var(--dark-color);
    color: var(--light-color);
    border-right: none;
    padding-left: 1.25rem;
}

.search-container .form-control {
    border-color: var(--primary-color);
    background-color: var(--dark-color);
    color: var(--light-color);
    border-left: none;
    padding-left: 0;
    font-size: 0.95rem;
    height: 46px;
}

.search-container .form-control:focus {
    box-shadow: none;
    border-color: var(--primary-color);
}

.search-container .btn {
    border-color: var(--primary-color);
    background-color: var(--dark-color);
    color: var(--light-color);
    border-left: none;
}

.search-container .btn:hover {
    background-color: var(--primary-color);
    color: var(--light-color);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--dark-color);
    border: 1px solid var(--primary-color);
    border-radius: 8px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    margin-top: 5px;
}

.search-suggestion {
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.search-suggestion:hover {
    background-color: rgba(217, 0, 0, 0.2);
}

.active-filters {
    margin-top: 10px;
}

.active-filters .badge {
    font-size: 0.9rem;
    padding: 8px 12px;
    border-radius: 20px;
    background-color: var(--primary-color);
    color: var(--light-color);
    display: inline-flex;
    align-items: center;
}

.active-filters .badge i {
    margin-left: 8px;
    cursor: pointer;
}

.hashtag {
    color: var(--primary-color);
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.hashtag:hover {
    text-decoration: underline;
    color: var(--secondary-color);
}

/* Pulse animation for hashtags */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.hashtag.pulse {
    animation: pulse 0.5s ease-in-out;
}

/* Mobile optimizations */
@media (max-width: 576px) {
    .search-container .form-control {
        height: 42px;
        font-size: 0.9rem;
    }
    
    .search-container .input-group-text {
        padding-left: 1rem;
    }
    
    .active-filters .badge {
        font-size: 0.8rem;
        padding: 6px 10px;
    }
}/* Search 
and Filter Styles */
.search-container {
    background: linear-gradient(145deg, rgba(26, 26, 26, 0.95), rgba(40, 40, 40, 0.95));
    border: 1px solid rgba(217, 0, 0, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.search-container:focus-within {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 15px rgba(217, 0, 0, 0.2);
    border-color: rgba(217, 0, 0, 0.4);
}

.search-container .input-group-text {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}

.search-container .form-control {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    font-size: 1rem;
    padding: 0.75rem 1rem;
    background-color: #1a1a1a;
    border-color: #dc3545;
    color: #fff;
    transition: all 0.3s ease;
}

.search-container .form-control:focus {
    background-color: #1a1a1a;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
    border-color: #dc3545;
}

.search-container .btn-outline-danger {
    border-radius: 10px;
    padding: 0.375rem 0.75rem;
}

.search-suggestions {
    background-color: #1a1a1a;
    border: 1px solid rgba(217, 0, 0, 0.2);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
}

.search-suggestion {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.search-suggestion:last-child {
    border-bottom: none;
}

.search-suggestion:hover {
    background-color: rgba(217, 0, 0, 0.2);
}

.active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.active-filters .badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: #dc3545;
    color: white;
    border-radius: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.active-filters .badge:hover {
    background-color: #c82333;
}

.active-filters .badge i {
    cursor: pointer;
}

/* Hashtag Styling */
.hashtag {
    color: #dc3545;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-block;
}

.hashtag:hover {
    color: #ff6b6b;
    text-decoration: underline;
    transform: translateY(-1px);
}

/* No Results Styling */
.no-results-message {
    text-align: center;
    padding: 3rem 1rem;
}

.no-results-message i {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.no-results-message h4 {
    color: #fff;
    margin-bottom: 1rem;
}

.no-results-message p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}
/* Service Container */
.service-container {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
}

.service-item {
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    overflow: hidden;
    position: relative;
    width: 200px;
    height: 200px;
    transition: all 0.3s ease;
}

.service-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(220, 53, 69, 0.2);
}

.service-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    text-align: center;
}

.service-details-container {
    background: #1a1a1a;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    display: none; /* Initially hidden */
}
/*
 Infinite Scroll Styles */
.infinite-scroll-loading,
.infinite-scroll-error,
.infinite-scroll-end {
    margin: 2rem 0;
    padding: 1.5rem;
    border-radius: 0.5rem;
    background-color: rgba(26, 26, 26, 0.8);
    border: 1px solid rgba(217, 0, 0, 0.2);
}

.infinite-scroll-loading .spinner-border {
    width: 1.5rem;
    height: 1.5rem;
}

.infinite-scroll-error {
    border-color: rgba(255, 193, 7, 0.3);
    background-color: rgba(255, 193, 7, 0.1);
}

.infinite-scroll-end {
    border-color: rgba(40, 167, 69, 0.3);
    background-color: rgba(40, 167, 69, 0.1);
}

.retry-load {
    transition: all 0.3s ease;
}

.retry-load:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(217, 0, 0, 0.3);
}

/* Smooth animations for new posts */
.social-post {
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.social-post.loading {
    opacity: 0;
    transform: translateY(20px);
}

/* Loading skeleton for posts */
.post-skeleton {
    background: linear-gradient(90deg, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Responsive adjustments for infinite scroll */
@media (max-width: 768px) {
    .infinite-scroll-loading,
    .infinite-scroll-error,
    .infinite-scroll-end {
        margin: 1rem 0;
        padding: 1rem;
    }
    
    .infinite-scroll-loading .spinner-border {
        width: 1.25rem;
        height: 1.25rem;
    }
}

/* Performance optimization for scroll */
.posts-feed {
    contain: layout style paint;
}

/* Smooth scroll behavior */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.retry-load:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}/* 
Infinite Scroll Styles */
.infinite-scroll-loading,
.infinite-scroll-error,
.infinite-scroll-end {
    background: rgba(26, 26, 26, 0.8);
    border-radius: 10px;
    margin: 1rem 0;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(217, 0, 0, 0.1);
}

.infinite-scroll-loading .spinner-border {
    width: 1.5rem;
    height: 1.5rem;
    border-width: 2px;
}

.infinite-scroll-error .btn {
    transition: all 0.3s ease;
}

.infinite-scroll-error .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(217, 0, 0, 0.3);
}

.infinite-scroll-end {
    opacity: 0.8;
}

/* Post Loading Animation */
.social-post.post-loading {
    transform: translateY(30px);
    opacity: 0;
}

.social-post {
    transition: all 0.3s ease;
}

/* Smooth scroll behavior */
html {
    scroll-behavior: smooth;
}

/* Loading state improvements */
.posts-feed {
    min-height: 200px;
}

.social-post {
    will-change: transform, opacity;
}

/* Enhanced loading spinner */
.infinite-scroll-loading .spinner-border {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Fade in animation for new posts */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.post-fade-in {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}