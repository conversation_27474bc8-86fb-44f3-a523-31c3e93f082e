/**
 * Enhanced navbar and dropdown menu fix for Barber Brothers Legacy website
 * This script ensures that dropdown menus close properly and hamburger menu works correctly
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing navbar and dropdown fixes...');

    // Wait for <PERSON>tra<PERSON> to load
    if (typeof bootstrap !== 'undefined') {
        initBootstrapNavbar();
    } else {
        // Fallback if Bootstrap isn't loaded
        setTimeout(() => {
            if (typeof bootstrap !== 'undefined') {
                initBootstrapNavbar();
            } else {
                initHamburgerMenu();
            }
        }, 100);
    }

    // Initialize dropdown close functionality
    initDropdownClose();

    // Initialize click outside to close menu
    initClickOutsideClose();
});

/**
 * Initialize Bootstrap navbar functionality
 */
function initBootstrapNavbar() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (!navbarToggler || !navbarCollapse) {
        console.warn('Navbar elements not found');
        return;
    }

    // Initialize Bootstrap Collapse
    try {
        const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
            toggle: false
        });

        // Add click handler for toggler
        navbarToggler.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Bootstrap hamburger menu clicked');
            bsCollapse.toggle();
        });

        console.log('Bootstrap navbar functionality initialized');
    } catch (error) {
        console.warn('Bootstrap Collapse failed, using fallback:', error);
        initHamburgerMenu();
    }
}

/**
 * Initialize hamburger menu functionality
 */
function initHamburgerMenu() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (!navbarToggler || !navbarCollapse) {
        console.warn('Navbar elements not found');
        return;
    }

    // Ensure proper Bootstrap functionality
    navbarToggler.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('Hamburger menu clicked');

        // Toggle the collapse manually if Bootstrap isn't working
        const isExpanded = navbarCollapse.classList.contains('show');

        if (isExpanded) {
            // Close the menu
            navbarCollapse.classList.remove('show');
            navbarToggler.setAttribute('aria-expanded', 'false');
            navbarToggler.classList.add('collapsed');
        } else {
            // Open the menu
            navbarCollapse.classList.add('show');
            navbarToggler.setAttribute('aria-expanded', 'true');
            navbarToggler.classList.remove('collapsed');
        }
    });

    console.log('Hamburger menu functionality initialized');
}

/**
 * Initialize dropdown close functionality
 */
function initDropdownClose() {
    // Get all navigation links
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

    // Add click event listener to each navigation link
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Check if the navbar is expanded (in mobile view)
            const navbarCollapse = document.querySelector('.navbar-collapse.show');
            if (navbarCollapse) {
                // Close the menu
                closeNavbarMenu();
            }
        });
    });

    console.log('Dropdown close functionality initialized');
}

/**
 * Initialize click outside to close menu
 */
function initClickOutsideClose() {
    document.addEventListener('click', function(e) {
        const navbar = document.querySelector('.navbar');
        const navbarCollapse = document.querySelector('.navbar-collapse.show');

        // If menu is open and click is outside navbar, close it
        if (navbarCollapse && !navbar.contains(e.target)) {
            closeNavbarMenu();
        }
    });

    console.log('Click outside close functionality initialized');
}

/**
 * Close the navbar menu
 */
function closeNavbarMenu() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (navbarCollapse && navbarCollapse.classList.contains('show')) {
        // Try Bootstrap method first
        if (typeof bootstrap !== 'undefined') {
            try {
                const bsCollapse = bootstrap.Collapse.getInstance(navbarCollapse);
                if (bsCollapse) {
                    bsCollapse.hide();
                } else {
                    // Fallback to manual method
                    manualCloseMenu(navbarToggler, navbarCollapse);
                }
            } catch (error) {
                console.warn('Bootstrap close failed, using manual method:', error);
                manualCloseMenu(navbarToggler, navbarCollapse);
            }
        } else {
            manualCloseMenu(navbarToggler, navbarCollapse);
        }
        console.log('Navbar menu closed');
    }
}

/**
 * Manually close the menu
 */
function manualCloseMenu(navbarToggler, navbarCollapse) {
    navbarCollapse.classList.remove('show');
    if (navbarToggler) {
        navbarToggler.setAttribute('aria-expanded', 'false');
        navbarToggler.classList.add('collapsed');
    }
}

// Add additional debugging and error handling
window.addEventListener('load', function() {
    console.log('Page loaded, checking navbar functionality...');

    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (navbarToggler && navbarCollapse) {
        console.log('✅ Navbar elements found');
        console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
    } else {
        console.error('❌ Navbar elements missing:', {
            toggler: !!navbarToggler,
            collapse: !!navbarCollapse
        });
    }
});