/**
 * Barber Brothers Community Hub
 * A comprehensive community system with Feed, Profile, and Discover functionality
 */

class BarberBrothersCommunity {
    constructor() {
        this.currentUser = null;
        this.currentView = 'feed';
        this.posts = [];
        this.users = [];
        this.isInitialized = false;
        
        // Sample data
        this.initializeSampleData();
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    /**
     * Initialize the community hub
     */
    init() {
        console.log('🏛️ Community Hub: Initializing...');
        
        // Wait for Firebase auth to be ready
        this.waitForAuth().then(() => {
            this.setupEventListeners();
            this.loadView('feed');
            this.isInitialized = true;
            console.log('✅ Community Hub: Initialized successfully');
        });
    }

    /**
     * Wait for Firebase authentication to be ready
     */
    async waitForAuth() {
        return new Promise((resolve) => {
            const checkAuth = () => {
                if (window.firebase && window.firebase.auth) {
                    // Listen for auth state changes
                    window.firebase.auth().onAuthStateChanged((user) => {
                        this.currentUser = user;
                        this.updateAuthUI();
                    });
                    resolve();
                } else {
                    setTimeout(checkAuth, 500);
                }
            };
            checkAuth();
        });
    }

    /**
     * Initialize sample data for demonstration
     */
    initializeSampleData() {
        // Only Barber Brothers Legacy content - no other businesses
        this.users = [
            {
                id: 'andre_barber',
                name: 'Andre The Barber',
                avatar: 'images/time.jpeg',
                type: 'barber',
                location: 'Douglasville, GA',
                specialties: ['Fades', 'Beard Trims', 'Classic Cuts', 'Hot Towel Shaves'],
                rating: 4.9,
                followers: 1250,
                following: 89,
                verified: true,
                bio: 'Master Barber with 20+ years experience from New Orleans, now in Douglasville. Precision cuts and classic styles at Barber Brothers Legacy.',
                posts: 156
            },
            {
                id: 'barber_brothers_media',
                name: 'Barber Brothers Legacy',
                avatar: 'images/time.jpeg',
                type: 'business',
                location: 'Douglasville, GA',
                specialties: ['Premium Cuts', 'Professional Service', 'Legacy Experience'],
                rating: 4.9,
                followers: 2340,
                following: 45,
                verified: true,
                bio: 'Official Barber Brothers Legacy account. Where style meets precision. Premium barbering experience in Douglasville, Georgia.',
                posts: 298
            }
        ];

        // Sample posts - Only Barber Brothers Legacy content
        this.posts = [
            {
                id: 1,
                author: this.users[0], // Andre The Barber
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
                content: 'Just finished this amazing fade at Barber Brothers Legacy! Clean lines and perfect blend. 20+ years of experience shows in every cut. #BarberBrothersLegacy #FreshCuts #BarberLife #Douglasville',
                image: 'images/IMG_1073.jpeg',
                likes: 45,
                comments: 12,
                shares: 3,
                hashtags: ['BarberBrothersLegacy', 'FreshCuts', 'BarberLife', 'Douglasville']
            },
            {
                id: 2,
                author: this.users[1], // Barber Brothers Legacy
                timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
                content: 'Welcome to Barber Brothers Legacy! Tips for maintaining your fresh cut:\n\n1. Use quality hair products\n2. Regular washing with proper shampoo\n3. Light trimming of edges\n4. Book your next appointment\n\nWhere style meets precision! #BarberBrothersLegacy #HairCare #PremiumCuts',
                likes: 67,
                comments: 18,
                shares: 25,
                hashtags: ['BarberBrothersLegacy', 'HairCare', 'PremiumCuts']
            },
            {
                id: 3,
                author: this.users[0], // Andre The Barber
                timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
                content: 'Another satisfied client at Barber Brothers Legacy! From New Orleans techniques to Georgia precision. This is what 20+ years of experience delivers. 💈 #BarberBrothersLegacy #NewOrleans #MasterBarber #Precision',
                image: 'images/IMG_0920.jpeg',
                likes: 89,
                comments: 31,
                shares: 12,
                hashtags: ['BarberBrothersLegacy', 'NewOrleans', 'MasterBarber', 'Precision']
            },
            {
                id: 4,
                author: this.users[1], // Barber Brothers Legacy
                timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
                content: '🔥 TRUST YOUR STRUGGLE 🔥\n\nAt Barber Brothers Legacy, every cut tells a story. From classic styles to modern fades, we bring precision and passion to every chair. Book your appointment today! #BarberBrothersLegacy #TrustYourStruggle #BookNow',
                image: 'images/IMG_0980.jpeg',
                likes: 156,
                comments: 42,
                shares: 28,
                hashtags: ['BarberBrothersLegacy', 'TrustYourStruggle', 'BookNow']
            },
            {
                id: 5,
                author: this.users[0], // Andre The Barber
                timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
                content: 'Building the legacy one cut at a time. From the streets of New Orleans to Douglasville, Georgia - the craft continues. This isn\'t just a barbershop, it\'s a legacy in the making. 💈 #BarberBrothersLegacy #Legacy #Craft',
                image: 'images/IMG_0994.jpeg',
                likes: 78,
                comments: 19,
                shares: 8,
                hashtags: ['BarberBrothersLegacy', 'Legacy', 'Craft']
            }
        ];
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Navigation buttons
        document.querySelectorAll('[data-view]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.target.closest('[data-view]').dataset.view;
                this.loadView(view);
            });
        });

        // Global click handler for dynamic content
        document.addEventListener('click', (e) => {
            // Handle like buttons
            if (e.target.closest('.like-btn')) {
                this.handleLike(e.target.closest('.like-btn'));
            }
            
            // Handle comment buttons
            if (e.target.closest('.comment-btn')) {
                this.handleComment(e.target.closest('.comment-btn'));
            }
            
            // Handle follow buttons
            if (e.target.closest('.follow-btn')) {
                this.handleFollow(e.target.closest('.follow-btn'));
            }
            
            // Handle search
            if (e.target.closest('.search-btn')) {
                this.handleSearch();
            }
        });

        // Search input
        document.addEventListener('input', (e) => {
            if (e.target.matches('.search-input')) {
                this.handleSearchInput(e.target.value);
            }
        });
    }

    /**
     * Load a specific view
     */
    loadView(viewName) {
        console.log(`🔄 Loading ${viewName} view`);
        
        // Update navigation
        document.querySelectorAll('[data-view]').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${viewName}"]`).classList.add('active');
        
        // Hide all views
        document.querySelectorAll('.community-view').forEach(view => {
            view.style.display = 'none';
            view.classList.remove('active');
        });
        
        // Show selected view
        const viewElement = document.getElementById(`${viewName}-view`);
        viewElement.style.display = 'block';
        viewElement.classList.add('active');
        
        // Load content based on view
        switch (viewName) {
            case 'feed':
                this.loadFeedView();
                break;
            case 'profile':
                this.loadProfileView();
                break;
            case 'discover':
                this.loadDiscoverView();
                break;
        }
        
        this.currentView = viewName;
    }

    /**
     * Update authentication UI
     */
    updateAuthUI() {
        // This will be called when auth state changes
        // Update post composers and interaction buttons based on auth state
        if (this.currentView) {
            this.loadView(this.currentView);
        }
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return this.currentUser !== null;
    }

    /**
     * Get current user display info
     */
    getCurrentUserInfo() {
        if (this.currentUser) {
            return {
                name: this.currentUser.displayName || 'User',
                avatar: this.currentUser.photoURL || 'images/time.jpeg',
                email: this.currentUser.email
            };
        }
        return null;
    }

    /**
     * Format timestamp for display
     */
    formatTimestamp(timestamp) {
        const now = new Date();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 1) return 'Just now';
        if (minutes < 60) return `${minutes}m ago`;
        if (hours < 24) return `${hours}h ago`;
        if (days < 7) return `${days}d ago`;
        return timestamp.toLocaleDateString();
    }

    /**
     * Load Feed view
     */
    loadFeedView() {
        const feedView = document.getElementById('feed-view');
        const isAuth = this.isAuthenticated();
        const userInfo = this.getCurrentUserInfo();

        feedView.innerHTML = `
            <div class="feed-container">
                <!-- Post Composer -->
                <div class="post-composer mb-4 p-3 bg-black bg-opacity-50 rounded">
                    ${this.createPostComposer(isAuth, userInfo)}
                </div>

                <!-- Posts Feed -->
                <div class="posts-container">
                    ${this.posts.map(post => this.createPostHTML(post)).join('')}
                </div>

                <!-- Load More -->
                <div class="text-center mt-4">
                    <button class="btn btn-outline-danger load-more-btn">
                        <i class="fas fa-plus me-2"></i>Load More Posts
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Create post composer
     */
    createPostComposer(isAuth, userInfo) {
        if (isAuth && userInfo) {
            return `
                <div class="d-flex align-items-center mb-3">
                    <img src="${userInfo.avatar}" alt="${userInfo.name}" class="rounded-circle me-3" width="40" height="40">
                    <input type="text" class="form-control bg-dark text-white border-danger post-input"
                           placeholder="Share your latest work or thoughts...">
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <div class="post-options">
                        <button class="btn btn-outline-light btn-sm me-2">
                            <i class="fas fa-image me-1"></i>Photo
                        </button>
                        <button class="btn btn-outline-light btn-sm">
                            <i class="fas fa-video me-1"></i>Video
                        </button>
                    </div>
                    <button class="btn btn-danger post-submit-btn">
                        <i class="fas fa-paper-plane me-1"></i>Post
                    </button>
                </div>
            `;
        } else {
            return `
                <div class="text-center py-4">
                    <i class="fas fa-lock fa-2x text-muted mb-3"></i>
                    <h5 class="text-white mb-3">Sign in to participate</h5>
                    <p class="text-muted mb-3">Join the Barber Brothers community to share posts, like content, and connect with others.</p>
                    <button class="btn btn-danger" onclick="document.getElementById('google-signin-btn').click()">
                        <i class="fas fa-user me-2"></i>Sign In
                    </button>
                </div>
            `;
        }
    }

    /**
     * Create HTML for a single post
     */
    createPostHTML(post) {
        return `
            <div class="post-card mb-4 p-3 bg-black bg-opacity-50 rounded border border-danger border-opacity-25" data-post-id="${post.id}">
                <div class="post-header d-flex align-items-center mb-3">
                    <img src="${post.author.avatar}" alt="${post.author.name}" class="rounded-circle me-3" width="40" height="40">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center">
                            <h6 class="text-white mb-0 me-2">${post.author.name}</h6>
                            ${post.author.verified ? '<i class="fas fa-check-circle text-primary" title="Verified"></i>' : ''}
                            ${post.author.type === 'barber' ? '<span class="badge bg-danger ms-2">Barber</span>' : ''}
                        </div>
                        <small class="text-muted">${this.formatTimestamp(post.timestamp)} • ${post.author.location}</small>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-link text-muted" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-dark">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-bookmark me-2"></i>Save Post</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-flag me-2"></i>Report</a></li>
                        </ul>
                    </div>
                </div>

                <div class="post-content mb-3">
                    <p class="text-white">${this.formatPostContent(post.content)}</p>
                    ${post.image ? `<img src="${post.image}" alt="Post image" class="img-fluid rounded mb-3" style="max-height: 400px; width: 100%; object-fit: cover;">` : ''}
                </div>

                <div class="post-stats d-flex align-items-center text-muted mb-3">
                    <span class="like-count"><i class="fas fa-heart text-danger me-1"></i> ${post.likes}</span>
                    <span class="ms-3"><i class="fas fa-comment me-1"></i> ${post.comments}</span>
                    <span class="ms-3"><i class="fas fa-share me-1"></i> ${post.shares}</span>
                </div>

                <hr class="border-secondary">

                <div class="post-actions d-flex">
                    <button class="btn btn-link text-white flex-grow-1 like-btn" data-post-id="${post.id}">
                        <i class="far fa-heart me-1"></i> Like
                    </button>
                    <button class="btn btn-link text-white flex-grow-1 comment-btn" data-post-id="${post.id}">
                        <i class="far fa-comment me-1"></i> Comment
                    </button>
                    <button class="btn btn-link text-white flex-grow-1">
                        <i class="far fa-share-square me-1"></i> Share
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Format post content (convert hashtags to links)
     */
    formatPostContent(content) {
        return content.replace(/#(\w+)/g, '<span class="hashtag text-danger">#$1</span>');
    }

    /**
     * Load Profile view
     */
    loadProfileView() {
        const profileView = document.getElementById('profile-view');
        const isAuth = this.isAuthenticated();
        const userInfo = this.getCurrentUserInfo();

        if (!isAuth) {
            profileView.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-user-circle fa-4x text-muted mb-4"></i>
                    <h3 class="text-white mb-3">Sign in to view your profile</h3>
                    <p class="text-muted mb-4">Create your barber profile and showcase your work to the community.</p>
                    <button class="btn btn-danger" onclick="document.getElementById('google-signin-btn').click()">
                        <i class="fas fa-user me-2"></i>Sign In
                    </button>
                </div>
            `;
            return;
        }

        profileView.innerHTML = `
            <div class="profile-container">
                <!-- Profile Header -->
                <div class="profile-header mb-4 p-4 bg-black bg-opacity-50 rounded">
                    <div class="row align-items-center">
                        <div class="col-md-3 text-center">
                            <img src="${userInfo.avatar}" alt="${userInfo.name}" class="rounded-circle mb-3" width="120" height="120">
                            <button class="btn btn-outline-danger btn-sm">Edit Photo</button>
                        </div>
                        <div class="col-md-9">
                            <div class="d-flex align-items-center mb-2">
                                <h3 class="text-white me-3">${userInfo.name}</h3>
                                <button class="btn btn-outline-light btn-sm">Edit Profile</button>
                            </div>
                            <p class="text-muted mb-3">${userInfo.email}</p>
                            <div class="profile-stats d-flex gap-4 mb-3">
                                <div class="stat">
                                    <strong class="text-white">12</strong>
                                    <small class="text-muted d-block">Posts</small>
                                </div>
                                <div class="stat">
                                    <strong class="text-white">156</strong>
                                    <small class="text-muted d-block">Followers</small>
                                </div>
                                <div class="stat">
                                    <strong class="text-white">89</strong>
                                    <small class="text-muted d-block">Following</small>
                                </div>
                            </div>
                            <p class="text-white">Passionate about great haircuts and connecting with the barber community. Always learning new techniques!</p>
                        </div>
                    </div>
                </div>

                <!-- Profile Tabs -->
                <div class="profile-tabs mb-4">
                    <div class="btn-group w-100" role="group">
                        <button class="btn btn-outline-danger active profile-tab" data-tab="posts">
                            <i class="fas fa-th-large me-2"></i>Posts
                        </button>
                        <button class="btn btn-outline-danger profile-tab" data-tab="saved">
                            <i class="fas fa-bookmark me-2"></i>Saved
                        </button>
                        <button class="btn btn-outline-danger profile-tab" data-tab="settings">
                            <i class="fas fa-cog me-2"></i>Settings
                        </button>
                    </div>
                </div>

                <!-- Profile Content -->
                <div class="profile-content">
                    <div class="text-center py-5">
                        <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                        <h5 class="text-white mb-3">No posts yet</h5>
                        <p class="text-muted mb-3">Share your first post to start building your profile!</p>
                        <button class="btn btn-danger" onclick="window.BarberBrothersCommunity.loadView('feed')">
                            Create Your First Post
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Load Discover view
     */
    loadDiscoverView() {
        const discoverView = document.getElementById('discover-view');

        discoverView.innerHTML = `
            <div class="discover-container">
                <!-- Search Bar -->
                <div class="search-section mb-4 p-3 bg-black bg-opacity-50 rounded">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="input-group">
                                <input type="text" class="form-control bg-dark text-white border-danger search-input"
                                       placeholder="Search Barber Brothers Legacy content, services, or team...">
                                <button class="btn btn-danger search-btn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select bg-dark text-white border-danger">
                                <option value="">Barber Brothers Legacy</option>
                                <option value="services">Our Services</option>
                                <option value="team">Our Team</option>
                                <option value="posts">Posts & Updates</option>
                                <option value="activities">Community Activities</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Barber Brothers Legacy Team -->
                    <div class="col-md-8">
                        <h4 class="text-white mb-3"><i class="fas fa-star me-2"></i>Barber Brothers Legacy Team</h4>
                        <div class="barbers-grid">
                            ${this.users.map(user => this.createTeamMemberCard(user)).join('')}
                        </div>

                        <!-- Community Activities -->
                        <div class="mt-4">
                            <h4 class="text-white mb-3"><i class="fas fa-calendar me-2"></i>Community Activities</h4>
                            <div class="activities-grid">
                                ${this.createCommunityActivities()}
                            </div>
                        </div>
                    </div>

                    <!-- Trending & Quick Actions -->
                    <div class="col-md-4">
                        <!-- Trending at Barber Brothers Legacy -->
                        <div class="trending-section mb-4 p-3 bg-black bg-opacity-50 rounded">
                            <h5 class="text-white mb-3"><i class="fas fa-fire me-2"></i>Trending at BBL</h5>
                            <div class="trending-item d-flex justify-content-between align-items-center mb-2 p-2 rounded bg-dark">
                                <span class="text-white">#BarberBrothersLegacy</span>
                                <span class="badge bg-danger">342</span>
                            </div>
                            <div class="trending-item d-flex justify-content-between align-items-center mb-2 p-2 rounded bg-dark">
                                <span class="text-white">#TrustYourStruggle</span>
                                <span class="badge bg-danger">289</span>
                            </div>
                            <div class="trending-item d-flex justify-content-between align-items-center mb-2 p-2 rounded bg-dark">
                                <span class="text-white">#PremiumCuts</span>
                                <span class="badge bg-danger">156</span>
                            </div>
                            <div class="trending-item d-flex justify-content-between align-items-center p-2 rounded bg-dark">
                                <span class="text-white">#Douglasville</span>
                                <span class="badge bg-danger">134</span>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="quick-actions p-3 bg-black bg-opacity-50 rounded">
                            <h5 class="text-white mb-3"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-danger" onclick="document.getElementById('contact').scrollIntoView({behavior: 'smooth'})">
                                    <i class="fas fa-calendar me-2"></i>Book with Andre
                                </button>
                                <button class="btn btn-outline-danger" onclick="window.open('https://maps.google.com/?q=Douglasville+GA+barbershop', '_blank')">
                                    <i class="fas fa-map-marker-alt me-2"></i>Find Our Location
                                </button>
                                <button class="btn btn-outline-danger" onclick="document.getElementById('services').scrollIntoView({behavior: 'smooth'})">
                                    <i class="fas fa-cut me-2"></i>View Services
                                </button>
                                <button class="btn btn-outline-danger" onclick="document.getElementById('my-work').scrollIntoView({behavior: 'smooth'})">
                                    <i class="fas fa-images me-2"></i>See Our Work
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create team member card for discover view
     */
    createTeamMemberCard(user) {
        const buttonText = user.type === 'business' ? 'Follow Page' : 'Follow';
        const buttonIcon = user.type === 'business' ? 'fas fa-thumbs-up' : 'fas fa-plus';

        return `
            <div class="barber-card mb-3 p-3 bg-black bg-opacity-50 rounded border border-danger border-opacity-25">
                <div class="d-flex align-items-center mb-3">
                    <img src="${user.avatar}" alt="${user.name}" class="rounded-circle me-3" width="60" height="60">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-1">
                            <h6 class="text-white mb-0 me-2">${user.name}</h6>
                            ${user.verified ? '<i class="fas fa-check-circle text-primary" title="Verified"></i>' : ''}
                            ${user.type === 'business' ? '<span class="badge bg-danger ms-2">Official</span>' : '<span class="badge bg-secondary ms-2">Master Barber</span>'}
                        </div>
                        <p class="text-muted mb-1"><i class="fas fa-map-marker-alt me-1"></i>${user.location}</p>
                        <div class="rating mb-1">
                            <span class="text-warning">★★★★★</span>
                            <span class="text-white ms-1">${user.rating}</span>
                            <small class="text-muted ms-1">(${user.followers} followers)</small>
                        </div>
                    </div>
                    <button class="btn btn-outline-danger btn-sm follow-btn" data-user-id="${user.id}">
                        <i class="${buttonIcon} me-1"></i>${buttonText}
                    </button>
                </div>

                <div class="specialties mb-3">
                    ${user.specialties.map(specialty => `<span class="badge bg-secondary me-1">${specialty}</span>`).join('')}
                </div>

                <p class="text-white small mb-3">${user.bio}</p>

                <div class="barber-stats d-flex justify-content-between text-center">
                    <div>
                        <strong class="text-white d-block">${user.posts}</strong>
                        <small class="text-muted">Posts</small>
                    </div>
                    <div>
                        <strong class="text-white d-block">${user.followers}</strong>
                        <small class="text-muted">Followers</small>
                    </div>
                    <div>
                        <strong class="text-white d-block">${user.following}</strong>
                        <small class="text-muted">Following</small>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create community activities section
     */
    createCommunityActivities() {
        const activities = [
            {
                title: 'Weekly Style Challenge',
                description: 'Share your best fade of the week',
                participants: 23,
                icon: 'fas fa-trophy',
                status: 'active'
            },
            {
                title: 'Client Testimonials',
                description: 'Share your Barber Brothers experience',
                participants: 45,
                icon: 'fas fa-star',
                status: 'ongoing'
            },
            {
                title: 'Behind the Scenes',
                description: 'See how the magic happens',
                participants: 67,
                icon: 'fas fa-camera',
                status: 'featured'
            }
        ];

        return activities.map(activity => `
            <div class="activity-card mb-3 p-3 bg-black bg-opacity-50 rounded border border-danger border-opacity-25">
                <div class="d-flex align-items-center mb-2">
                    <div class="activity-icon me-3">
                        <i class="${activity.icon} fa-2x text-danger"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="text-white mb-1">${activity.title}</h6>
                        <p class="text-muted mb-0 small">${activity.description}</p>
                    </div>
                    <span class="badge bg-${activity.status === 'active' ? 'success' : activity.status === 'featured' ? 'danger' : 'secondary'}">${activity.status}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">${activity.participants} participants</small>
                    <button class="btn btn-outline-danger btn-sm">Join</button>
                </div>
            </div>
        `).join('');
    }

    /**
     * Handle like button click
     */
    handleLike(btn) {
        if (!this.isAuthenticated()) {
            this.showToast('Please sign in to like posts', 'error');
            return;
        }

        const postId = btn.dataset.postId;
        const icon = btn.querySelector('i');
        const isLiked = icon.classList.contains('fas');

        if (isLiked) {
            // Unlike
            icon.classList.remove('fas', 'text-danger');
            icon.classList.add('far');
            btn.innerHTML = '<i class="far fa-heart me-1"></i> Like';
            this.updateLikeCount(postId, -1);
        } else {
            // Like
            icon.classList.remove('far');
            icon.classList.add('fas', 'text-danger');
            btn.innerHTML = '<i class="fas fa-heart text-danger me-1"></i> Liked';
            this.updateLikeCount(postId, 1);
            this.showToast('Post liked! ❤️');
        }
    }

    /**
     * Update like count for a post
     */
    updateLikeCount(postId, change) {
        const postCard = document.querySelector(`[data-post-id="${postId}"]`);
        const likeCountElement = postCard.querySelector('.like-count');
        if (likeCountElement) {
            const currentCount = parseInt(likeCountElement.textContent.match(/\d+/)[0]);
            const newCount = Math.max(0, currentCount + change);
            likeCountElement.innerHTML = `<i class="fas fa-heart text-danger me-1"></i> ${newCount}`;

            // Update the post data
            const post = this.posts.find(p => p.id == postId);
            if (post) {
                post.likes = newCount;
            }
        }
    }

    /**
     * Handle comment button click
     */
    handleComment(btn) {
        if (!this.isAuthenticated()) {
            this.showToast('Please sign in to comment', 'error');
            return;
        }

        const postCard = btn.closest('.post-card');
        let commentSection = postCard.querySelector('.comment-section');

        if (!commentSection) {
            const userInfo = this.getCurrentUserInfo();
            commentSection = document.createElement('div');
            commentSection.className = 'comment-section mt-3 p-3 bg-dark rounded';
            commentSection.innerHTML = `
                <div class="d-flex mb-3">
                    <img src="${userInfo.avatar}" alt="${userInfo.name}" class="rounded-circle me-2" width="30" height="30">
                    <input type="text" class="form-control bg-black text-white border-danger comment-input"
                           placeholder="Write a comment...">
                    <button class="btn btn-danger btn-sm ms-2 comment-submit">Post</button>
                </div>
                <div class="comments-list"></div>
            `;
            postCard.appendChild(commentSection);

            // Add comment functionality
            const commentInput = commentSection.querySelector('.comment-input');
            const commentSubmit = commentSection.querySelector('.comment-submit');

            commentSubmit.addEventListener('click', () => {
                const comment = commentInput.value.trim();
                if (comment) {
                    this.addComment(commentSection, comment, userInfo);
                    commentInput.value = '';
                }
            });

            commentInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const comment = commentInput.value.trim();
                    if (comment) {
                        this.addComment(commentSection, comment, userInfo);
                        commentInput.value = '';
                    }
                }
            });
        } else {
            // Toggle comment section
            commentSection.style.display = commentSection.style.display === 'none' ? 'block' : 'none';
        }
    }

    /**
     * Add a comment to a post
     */
    addComment(commentSection, commentText, userInfo) {
        const commentsList = commentSection.querySelector('.comments-list');
        const commentHTML = `
            <div class="comment mb-2 p-2 bg-black rounded">
                <div class="d-flex align-items-start">
                    <img src="${userInfo.avatar}" alt="${userInfo.name}" class="rounded-circle me-2" width="25" height="25">
                    <div>
                        <small class="text-white fw-bold">${userInfo.name}</small>
                        <small class="text-muted ms-2">now</small>
                        <p class="text-white mb-0 mt-1">${commentText}</p>
                    </div>
                </div>
            </div>
        `;
        commentsList.insertAdjacentHTML('beforeend', commentHTML);
        this.showToast('Comment added! 💬');
    }

    /**
     * Handle follow button click
     */
    handleFollow(btn) {
        if (!this.isAuthenticated()) {
            this.showToast('Please sign in to follow', 'error');
            return;
        }

        const isFollowing = btn.classList.contains('following');
        const isBusinessPage = btn.textContent.includes('Follow Page');

        if (isFollowing) {
            // Unfollow
            btn.classList.remove('following');
            if (isBusinessPage) {
                btn.innerHTML = '<i class="fas fa-thumbs-up me-1"></i>Follow Page';
            } else {
                btn.innerHTML = '<i class="fas fa-plus me-1"></i>Follow';
            }
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-danger');
            this.showToast('Unfollowed successfully');
        } else {
            // Follow
            btn.classList.add('following');
            if (isBusinessPage) {
                btn.innerHTML = '<i class="fas fa-check me-1"></i>Following Page';
            } else {
                btn.innerHTML = '<i class="fas fa-check me-1"></i>Following';
            }
            btn.classList.remove('btn-outline-danger');
            btn.classList.add('btn-success');
            this.showToast('Now following Barber Brothers Legacy! 👥');
        }
    }

    /**
     * Handle search functionality
     */
    handleSearch() {
        const searchInput = document.querySelector('.search-input');
        const query = searchInput.value.trim();

        if (query) {
            this.performSearch(query);
        }
    }

    /**
     * Handle search input changes (live search)
     */
    handleSearchInput(query) {
        if (query.length > 2) {
            // Perform live search after 3 characters
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.performSearch(query);
            }, 300);
        }
    }

    /**
     * Perform search and update results
     */
    performSearch(query) {
        console.log(`🔍 Searching Barber Brothers Legacy for: ${query}`);

        // Search within Barber Brothers Legacy content only
        const filteredTeam = this.users.filter(user =>
            user.name.toLowerCase().includes(query.toLowerCase()) ||
            user.location.toLowerCase().includes(query.toLowerCase()) ||
            user.specialties.some(specialty => specialty.toLowerCase().includes(query.toLowerCase())) ||
            user.bio.toLowerCase().includes(query.toLowerCase())
        );

        // Also search posts for relevant content
        const filteredPosts = this.posts.filter(post =>
            post.content.toLowerCase().includes(query.toLowerCase()) ||
            post.hashtags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
        );

        // Update the team grid
        const barbersGrid = document.querySelector('.barbers-grid');
        if (barbersGrid) {
            if (filteredTeam.length > 0) {
                barbersGrid.innerHTML = filteredTeam.map(user => this.createTeamMemberCard(user)).join('');
                this.showToast(`Found ${filteredTeam.length} team member(s)`, 'info');
            } else {
                barbersGrid.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-white">No results found</h5>
                        <p class="text-muted">Try searching for "Andre", "Legacy", "Douglasville", or service types</p>
                        <button class="btn btn-outline-danger mt-2" onclick="window.BarberBrothersCommunity.loadView('discover')">
                            Reset Search
                        </button>
                    </div>
                `;
                this.showToast(`No results found for "${query}"`, 'info');
            }
        }

        // Show related posts if any found
        if (filteredPosts.length > 0) {
            this.showToast(`Also found ${filteredPosts.length} related post(s)`, 'info');
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type === 'info' ? 'info' : 'success'} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// Initialize the community hub
const communityHub = new BarberBrothersCommunity();

// Export for global access
window.BarberBrothersCommunity = communityHub;
